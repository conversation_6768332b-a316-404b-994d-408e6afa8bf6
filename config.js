// 配置管理模块
class ConfigManager {
    constructor() {
        this.currentEditingProduct = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadRooms();
        this.loadProducts();
    }

    bindEvents() {
        // 添加直播间
        document.getElementById('addRoomBtn').addEventListener('click', () => {
            this.addRoom();
        });

        // 添加产品
        document.getElementById('addProductBtn').addEventListener('click', () => {
            this.showProductForm();
        });

        // 保存配置
        document.getElementById('saveConfigBtn').addEventListener('click', () => {
            this.exportConfig();
        });

        // 加载配置
        document.getElementById('loadConfigBtn').addEventListener('click', () => {
            document.getElementById('loadConfigFile').click();
        });

        document.getElementById('loadConfigFile').addEventListener('change', (e) => {
            this.importConfig(e.target.files[0]);
        });

        // 产品类型选择
        document.getElementById('productType').addEventListener('change', () => {
            this.updateProductForm();
        });

        // 批量导入
        document.getElementById('batchImportBtn').addEventListener('click', () => {
            this.showBatchImportModal();
        });

        document.getElementById('closeBatchImportModal').addEventListener('click', () => {
            this.hideBatchImportModal();
        });
    }

    // 加载直播间列表
    loadRooms() {
        const rooms = window.storageManager.getRooms();
        const roomsList = document.getElementById('roomsList');
        const selectedRoom = document.getElementById('selectedRoom');
        
        // 更新直播间列表
        roomsList.innerHTML = '';
        selectedRoom.innerHTML = '<option value="">选择直播间</option>';
        
        rooms.forEach(room => {
            // 添加到管理列表
            const roomDiv = document.createElement('div');
            roomDiv.className = 'flex items-center justify-between bg-gray-700 p-3 rounded';
            roomDiv.innerHTML = `
                <span class="font-medium text-lg">${room}</span>
                <button onclick="configManager.removeRoom('${room}')" 
                        class="text-red-400 hover:text-red-300 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            `;
            roomsList.appendChild(roomDiv);
            
            // 添加到选择下拉框
            const option = document.createElement('option');
            option.value = room;
            option.textContent = room;
            selectedRoom.appendChild(option);
        });
    }

    // 添加直播间
    addRoom() {
        const input = document.getElementById('newRoomName');
        const roomName = input.value.trim();
        
        if (!roomName) {
            this.showMessage('请输入直播间名称', 'error');
            return;
        }
        
        if (window.storageManager.addRoom(roomName)) {
            input.value = '';
            this.loadRooms();
            this.showMessage('直播间添加成功', 'success');
        } else {
            this.showMessage('直播间已存在或添加失败', 'error');
        }
    }

    // 删除直播间
    removeRoom(roomName) {
        if (confirm(`确定要删除直播间"${roomName}"吗？这将同时删除该直播间下的所有产品配置。`)) {
            if (window.storageManager.removeRoom(roomName)) {
                this.loadRooms();
                this.loadProducts();
                this.showMessage('直播间删除成功', 'success');
            } else {
                this.showMessage('删除失败', 'error');
            }
        }
    }

    // 显示产品配置表单
    showProductForm() {
        const selectedRoom = document.getElementById('selectedRoom').value;
        const productType = document.getElementById('productType').value;
        
        if (!selectedRoom) {
            this.showMessage('请先选择直播间', 'error');
            return;
        }
        
        if (!productType) {
            this.showMessage('请选择产品类型', 'error');
            return;
        }
        
        this.currentEditingProduct = null;
        this.updateProductForm();
        document.getElementById('productForm').classList.remove('hidden');
    }

    // 更新产品配置表单
    updateProductForm() {
        const productType = document.getElementById('productType').value;
        const productForm = document.getElementById('productForm');
        
        if (!productType) {
            productForm.classList.add('hidden');
            return;
        }
        
        let formHTML = '';
        
        if (productType === 'redpack') {
            formHTML = this.getRedpackForm();
        } else if (productType === 'lucky') {
            formHTML = this.getLuckyForm();
        }
        
        productForm.innerHTML = `
            <h3 class="text-lg font-semibold mb-4 text-yellow-400">
                ${productType === 'redpack' ? '红包配置' : '幸运助手配置'}
            </h3>
            ${formHTML}
            <div class="flex space-x-4 mt-6">
                <button onclick="configManager.saveProduct()" 
                        class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded transition-colors">
                    保存配置
                </button>
                <button onclick="configManager.cancelProductForm()" 
                        class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                    取消
                </button>
            </div>
        `;
    }

    // 获取红包配置表单
    getRedpackForm() {
        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">红包发送时间</label>
                    <input type="datetime-local" id="redpack_sendTime" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">开奖时间</label>
                    <select id="redpack_drawTime" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="3">3分钟</option>
                        <option value="10">10分钟</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">当前轮次</label>
                    <input type="number" id="redpack_currentRound" min="1" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">总轮次</label>
                    <input type="number" id="redpack_totalRounds" min="1" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">参与方式</label>
                    <select id="redpack_participationType" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="fanclub">粉丝团红包</option>
                        <option value="share">分享红包</option>
                        <option value="condition">条件型普通快币红包</option>
                        <option value="password">口令红包</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">快币数</label>
                    <input type="number" id="redpack_coins" min="0" max="99999999" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div class="md:col-span-2" id="redpack_passwordField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">口令内容</label>
                    <input type="text" id="redpack_password" 
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
            </div>
            <script>
                document.getElementById('redpack_participationType').addEventListener('change', function() {
                    const passwordField = document.getElementById('redpack_passwordField');
                    if (this.value === 'password') {
                        passwordField.style.display = 'block';
                    } else {
                        passwordField.style.display = 'none';
                    }
                });
            </script>
        `;
    }

    // 获取幸运助手配置表单
    getLuckyForm() {
        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">参与条件</label>
                    <select id="lucky_condition" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                        <option value="comment">评论</option>
                        <option value="like">点赞</option>
                        <option value="fanclub">粉丝团</option>
                        <option value="share">分享</option>
                        <option value="follow">关注主播</option>
                        <option value="watchtime">观看时长</option>
                        <option value="superfan">超粉团</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">中奖人数</label>
                    <input type="number" id="lucky_winners" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">当前轮次</label>
                    <input type="number" id="lucky_currentRound" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">总轮次</label>
                    <input type="number" id="lucky_totalRounds" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">开奖倒计时（分钟）</label>
                    <input type="number" id="lucky_countdown" min="1" max="60"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">定时发送时间</label>
                    <input type="datetime-local" id="lucky_scheduledTime"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium mb-2">奖品说明</label>
                    <textarea id="lucky_prizeDescription" rows="3"
                              class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"></textarea>
                </div>
                <div id="lucky_watchtimeField" style="display: none;">
                    <label class="block text-sm font-medium mb-2">观看时长要求（分钟）</label>
                    <input type="number" id="lucky_watchtime" min="1"
                           class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white">
                </div>
            </div>
            <script>
                document.getElementById('lucky_condition').addEventListener('change', function() {
                    const watchtimeField = document.getElementById('lucky_watchtimeField');
                    if (this.value === 'watchtime') {
                        watchtimeField.style.display = 'block';
                    } else {
                        watchtimeField.style.display = 'none';
                    }
                });
            </script>
        `;
    }

    // 保存产品配置
    saveProduct() {
        const selectedRoom = document.getElementById('selectedRoom').value;
        const productType = document.getElementById('productType').value;
        
        let productData = {
            room: selectedRoom,
            type: productType
        };
        
        if (productType === 'redpack') {
            productData = { ...productData, ...this.getRedpackData() };
        } else if (productType === 'lucky') {
            productData = { ...productData, ...this.getLuckyData() };
        }
        
        // 验证数据
        if (!this.validateProductData(productData)) {
            return;
        }
        
        if (this.currentEditingProduct) {
            // 更新现有产品
            if (window.storageManager.updateProduct(this.currentEditingProduct, productData)) {
                this.showMessage('产品配置更新成功', 'success');
            } else {
                this.showMessage('更新失败', 'error');
                return;
            }
        } else {
            // 添加新产品
            const productId = window.storageManager.addProduct(productData);
            if (productId) {
                this.showMessage('产品配置添加成功', 'success');
            } else {
                this.showMessage('添加失败', 'error');
                return;
            }
        }
        
        this.cancelProductForm();
        this.loadProducts();
        
        // 通知Dashboard更新
        if (window.dashboardManager) {
            window.dashboardManager.updateDashboard();
        }
    }

    // 获取红包数据
    getRedpackData() {
        return {
            sendTime: document.getElementById('redpack_sendTime').value,
            drawTime: parseInt(document.getElementById('redpack_drawTime').value),
            currentRound: parseInt(document.getElementById('redpack_currentRound').value),
            totalRounds: parseInt(document.getElementById('redpack_totalRounds').value),
            participationType: document.getElementById('redpack_participationType').value,
            coins: parseInt(document.getElementById('redpack_coins').value),
            password: document.getElementById('redpack_password')?.value || ''
        };
    }

    // 获取幸运助手数据
    getLuckyData() {
        return {
            condition: document.getElementById('lucky_condition').value,
            winners: parseInt(document.getElementById('lucky_winners').value),
            currentRound: parseInt(document.getElementById('lucky_currentRound').value),
            totalRounds: parseInt(document.getElementById('lucky_totalRounds').value),
            countdown: parseInt(document.getElementById('lucky_countdown').value),
            scheduledTime: document.getElementById('lucky_scheduledTime').value,
            prizeDescription: document.getElementById('lucky_prizeDescription').value,
            watchtime: parseInt(document.getElementById('lucky_watchtime')?.value || 0)
        };
    }

    // 验证产品数据
    validateProductData(data) {
        if (data.type === 'redpack') {
            if (!data.sendTime) {
                this.showMessage('请设置红包发送时间', 'error');
                return false;
            }
            if (!data.currentRound || !data.totalRounds) {
                this.showMessage('请设置轮次信息', 'error');
                return false;
            }
            if (data.currentRound > data.totalRounds) {
                this.showMessage('当前轮次不能大于总轮次', 'error');
                return false;
            }
            if (!data.coins || data.coins < 0) {
                this.showMessage('请设置有效的快币数', 'error');
                return false;
            }
        } else if (data.type === 'lucky') {
            if (!data.scheduledTime) {
                this.showMessage('请设置定时发送时间', 'error');
                return false;
            }
            if (!data.winners || data.winners < 1) {
                this.showMessage('请设置有效的中奖人数', 'error');
                return false;
            }
            if (!data.currentRound || !data.totalRounds) {
                this.showMessage('请设置轮次信息', 'error');
                return false;
            }
            if (data.currentRound > data.totalRounds) {
                this.showMessage('当前轮次不能大于总轮次', 'error');
                return false;
            }
            if (!data.countdown || data.countdown < 1 || data.countdown > 60) {
                this.showMessage('开奖倒计时必须在1-60分钟之间', 'error');
                return false;
            }
            if (data.condition === 'watchtime' && data.countdown <= data.watchtime) {
                this.showMessage('观看时长类幸运星，开奖倒计时必须大于观看时长', 'error');
                return false;
            }

            // 检查时间间隔限制
            if (!this.validateLuckyTimeInterval(data.scheduledTime)) {
                this.showMessage('幸运星活动时间间隔必须大于1分钟', 'error');
                return false;
            }
        }
        
        return true;
    }

    // 验证幸运星时间间隔
    validateLuckyTimeInterval(newTime) {
        const products = window.storageManager.getProducts();
        const newDateTime = new Date(newTime);
        
        for (let product of products) {
            if (product.type === 'lucky' && product.id !== this.currentEditingProduct) {
                const existingTime = new Date(product.scheduledTime);
                const timeDiff = Math.abs(newDateTime - existingTime);
                if (timeDiff < 60000) { // 小于1分钟
                    return false;
                }
            }
        }
        
        return true;
    }

    // 取消产品表单
    cancelProductForm() {
        document.getElementById('productForm').classList.add('hidden');
        document.getElementById('productType').value = '';
        this.currentEditingProduct = null;
    }

    // 加载产品配置列表
    loadProducts() {
        const products = window.storageManager.getProducts();
        const configList = document.getElementById('configList');
        
        if (products.length === 0) {
            configList.innerHTML = '<div class="text-gray-400 text-center py-8">暂无配置项</div>';
            return;
        }
        
        configList.innerHTML = products.map(product => {
            const timeStr = product.sendTime || product.scheduledTime;
            const time = timeStr ? new Date(timeStr).toLocaleString('zh-CN') : '未设置';
            
            let details = '';
            if (product.type === 'redpack') {
                details = `第${product.currentRound}/${product.totalRounds}轮 | ${product.coins}快币 | ${this.getParticipationTypeText(product.participationType)}`;
            } else if (product.type === 'lucky') {
                const roundInfo = product.currentRound && product.totalRounds ?
                    `第${product.currentRound}/${product.totalRounds}轮 | ` : '';
                details = `${roundInfo}${this.getConditionText(product.condition)} | ${product.winners}人中奖 | ${product.countdown}分钟倒计时`;
            }

            return `
                <div class="bg-gray-700 p-4 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center space-x-4 mb-2">
                                <span class="text-lg font-semibold text-blue-400">${product.room}</span>
                                <span class="px-2 py-1 rounded text-xs ${product.type === 'redpack' ? 'bg-orange-600' : 'bg-green-600'} text-white">
                                    ${product.type === 'redpack' ? '红包' : '幸运助手'}
                                </span>
                                <span class="text-yellow-400 font-mono">${time}</span>
                            </div>
                            <div class="text-sm text-gray-300">${details}</div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="configManager.copyProduct('${product.id}')"
                                    class="text-green-400 hover:text-green-300 transition-colors" title="复制配置">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                            <button onclick="configManager.editProduct('${product.id}')"
                                    class="text-blue-400 hover:text-blue-300 transition-colors" title="编辑配置">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button onclick="configManager.removeProduct('${product.id}')"
                                    class="text-red-400 hover:text-red-300 transition-colors" title="删除配置">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 编辑产品
    editProduct(productId) {
        const products = window.storageManager.getProducts();
        const product = products.find(p => p.id === productId);

        if (!product) return;

        this.currentEditingProduct = productId;
        this.fillProductForm(product);
    }

    // 复制产品
    copyProduct(productId) {
        const products = window.storageManager.getProducts();
        const product = products.find(p => p.id === productId);

        if (!product) return;

        this.currentEditingProduct = null; // 设为null表示新建
        this.fillProductForm(product, true); // true表示复制模式
    }

    // 填充产品表单
    fillProductForm(product, isCopy = false) {
        // 设置表单值
        document.getElementById('selectedRoom').value = product.room;
        document.getElementById('productType').value = product.type;

        this.updateProductForm();

        // 填充表单数据
        setTimeout(() => {
            if (product.type === 'redpack') {
                // 复制模式下清空时间，让用户重新设置
                document.getElementById('redpack_sendTime').value = isCopy ? '' : (product.sendTime || '');
                document.getElementById('redpack_drawTime').value = product.drawTime || '3';
                document.getElementById('redpack_currentRound').value = product.currentRound || '';
                document.getElementById('redpack_totalRounds').value = product.totalRounds || '';
                document.getElementById('redpack_participationType').value = product.participationType || 'fanclub';
                document.getElementById('redpack_coins').value = product.coins || '';
                if (document.getElementById('redpack_password')) {
                    document.getElementById('redpack_password').value = product.password || '';
                }
            } else if (product.type === 'lucky') {
                document.getElementById('lucky_condition').value = product.condition || 'comment';
                document.getElementById('lucky_winners').value = product.winners || '';
                document.getElementById('lucky_currentRound').value = product.currentRound || '';
                document.getElementById('lucky_totalRounds').value = product.totalRounds || '';
                document.getElementById('lucky_countdown').value = product.countdown || '';
                // 复制模式下清空时间，让用户重新设置
                document.getElementById('lucky_scheduledTime').value = isCopy ? '' : (product.scheduledTime || '');
                document.getElementById('lucky_prizeDescription').value = product.prizeDescription || '';
                if (document.getElementById('lucky_watchtime')) {
                    document.getElementById('lucky_watchtime').value = product.watchtime || '';
                }
            }
        }, 100);

        document.getElementById('productForm').classList.remove('hidden');

        if (isCopy) {
            this.showMessage('配置已复制，请修改时间后保存', 'info');
        }
    }

    // 删除产品
    removeProduct(productId) {
        if (confirm('确定要删除这个产品配置吗？')) {
            if (window.storageManager.removeProduct(productId)) {
                this.loadProducts();
                this.showMessage('产品配置删除成功', 'success');
                
                // 通知Dashboard更新
                if (window.dashboardManager) {
                    window.dashboardManager.updateDashboard();
                }
            } else {
                this.showMessage('删除失败', 'error');
            }
        }
    }

    // 导出配置
    exportConfig() {
        window.storageManager.exportConfig();
        this.showMessage('配置导出成功', 'success');
    }

    // 导入配置
    importConfig(file) {
        if (!file) return;
        
        window.storageManager.importConfig(file)
            .then(() => {
                this.loadRooms();
                this.loadProducts();
                this.showMessage('配置导入成功', 'success');
                
                // 通知Dashboard更新
                if (window.dashboardManager) {
                    window.dashboardManager.updateDashboard();
                }
            })
            .catch(error => {
                this.showMessage('导入失败: ' + error.message, 'error');
            });
    }

    // 获取参与方式文本
    getParticipationTypeText(type) {
        const types = {
            'fanclub': '粉丝团红包',
            'share': '分享红包',
            'condition': '条件型普通快币红包',
            'password': '口令红包'
        };
        return types[type] || type;
    }

    // 获取条件文本
    getConditionText(condition) {
        const conditions = {
            'comment': '评论',
            'like': '点赞',
            'fanclub': '粉丝团',
            'share': '分享',
            'follow': '关注主播',
            'watchtime': '观看时长',
            'superfan': '超粉团'
        };
        return conditions[condition] || condition;
    }

    // 显示批量导入模态框
    showBatchImportModal() {
        const content = `
            <div class="space-y-6">
                <!-- 格式说明 -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <h4 class="text-lg font-semibold mb-3 text-yellow-400">📋 批量导入格式说明</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-800 rounded p-3">
                            <h5 class="font-semibold text-orange-400 mb-2">🎁 红包格式</h5>
                            <div class="text-sm text-gray-300 mb-2">
                                直播间名,红包发送时间,开奖时间,当前轮次/总轮次,参与方式,快币数
                            </div>
                            <div class="text-xs text-green-400 bg-gray-900 rounded p-2 font-mono">
                                Before Party,2025-08-30 18:00:00,3min,1/4,粉丝团红包,66
                            </div>
                        </div>
                        <div class="bg-gray-800 rounded p-3">
                            <h5 class="font-semibold text-green-400 mb-2">🍀 幸运助手格式</h5>
                            <div class="text-sm text-gray-300 mb-2">
                                直播间名,参与条件,中奖人数,当前轮次/总轮次,开奖倒计时,定时发送时间,奖品说明
                            </div>
                            <div class="text-xs text-green-400 bg-gray-900 rounded p-2 font-mono">
                                Before Party,评论,100人,1/4,5min,2025-08-30 18:00:00,奖品
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 参数说明 -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <h4 class="text-lg font-semibold mb-3 text-cyan-400">⚙️ 参数说明</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <h6 class="font-semibold text-orange-400 mb-2">红包参数</h6>
                            <ul class="space-y-1 text-gray-300">
                                <li>• 开奖时间：3min 或 10min</li>
                                <li>• 参与方式：粉丝团红包、分享红包、条件型普通快币红包、口令红包</li>
                                <li>• 快币数：0-99999999</li>
                            </ul>
                        </div>
                        <div>
                            <h6 class="font-semibold text-green-400 mb-2">幸运助手参数</h6>
                            <ul class="space-y-1 text-gray-300">
                                <li>• 参与条件：评论、点赞、粉丝团、分享、关注主播、观看时长、超粉团</li>
                                <li>• 中奖人数：必须带"人"字，如"100人"</li>
                                <li>• 开奖倒计时：1-60min</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div>
                    <label class="block text-sm font-medium mb-2 text-white">批量导入内容（每行一个产品配置）</label>
                    <textarea id="batchImportText" rows="10"
                              class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white font-mono text-sm"
                              placeholder="请按照上述格式输入产品配置，每行一个产品..."></textarea>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-4">
                    <button onclick="configManager.processBatchImport()"
                            class="bg-green-600 hover:bg-green-700 px-6 py-2 rounded transition-colors">
                        开始导入
                    </button>
                    <button onclick="configManager.clearBatchImportText()"
                            class="bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded transition-colors">
                        清空内容
                    </button>
                    <button onclick="configManager.loadBatchImportExample()"
                            class="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded transition-colors">
                        加载示例
                    </button>
                </div>

                <!-- 导入结果 -->
                <div id="batchImportResult" class="hidden">
                    <h4 class="text-lg font-semibold mb-3 text-purple-400">导入结果</h4>
                    <div id="batchImportResultContent" class="bg-gray-900 rounded p-4 max-h-40 overflow-y-auto"></div>
                </div>
            </div>
        `;

        document.getElementById('batchImportContent').innerHTML = content;
        document.getElementById('batchImportModal').classList.remove('hidden');
    }

    // 隐藏批量导入模态框
    hideBatchImportModal() {
        document.getElementById('batchImportModal').classList.add('hidden');
    }

    // 清空批量导入文本
    clearBatchImportText() {
        document.getElementById('batchImportText').value = '';
    }

    // 加载批量导入示例
    loadBatchImportExample() {
        const example = `Before Party,2025-08-30 18:00:00,3min,1/4,粉丝团红包,1000
夏晚主会场（横屏）,2025-08-30 18:05:00,10min,2/3,口令红包,5000
Before Party,评论,50人,1/2,5min,2025-08-30 18:10:00,评论奖励
夏晚第二现场,观看时长,20人,1/1,8min,2025-08-30 18:15:00,观看时长奖励`;

        document.getElementById('batchImportText').value = example;
    }

    // 处理批量导入
    processBatchImport() {
        const text = document.getElementById('batchImportText').value.trim();
        if (!text) {
            this.showMessage('请输入导入内容', 'error');
            return;
        }

        const lines = text.split('\n').filter(line => line.trim());
        const results = [];
        let successCount = 0;
        let errorCount = 0;

        lines.forEach((line, index) => {
            try {
                const result = this.parseBatchImportLine(line.trim(), index + 1);
                if (result.success) {
                    const productId = window.storageManager.addProduct(result.product);
                    if (productId) {
                        results.push(`✅ 第${index + 1}行：${result.product.type === 'redpack' ? '红包' : '幸运助手'}导入成功`);
                        successCount++;
                    } else {
                        results.push(`❌ 第${index + 1}行：保存失败`);
                        errorCount++;
                    }
                } else {
                    results.push(`❌ 第${index + 1}行：${result.error}`);
                    errorCount++;
                }
            } catch (error) {
                results.push(`❌ 第${index + 1}行：解析失败 - ${error.message}`);
                errorCount++;
            }
        });

        // 显示结果
        const resultDiv = document.getElementById('batchImportResult');
        const resultContent = document.getElementById('batchImportResultContent');

        resultContent.innerHTML = `
            <div class="mb-4">
                <span class="text-green-400">成功：${successCount} 个</span>
                <span class="text-red-400 ml-4">失败：${errorCount} 个</span>
            </div>
            <div class="space-y-1 text-sm">
                ${results.map(result => `<div>${result}</div>`).join('')}
            </div>
        `;

        resultDiv.classList.remove('hidden');

        // 刷新配置列表
        this.loadProducts();

        // 通知Dashboard更新
        if (window.dashboardManager) {
            window.dashboardManager.updateDashboard();
        }

        this.showMessage(`批量导入完成：成功 ${successCount} 个，失败 ${errorCount} 个`, successCount > 0 ? 'success' : 'error');
    }

    // 解析批量导入行
    parseBatchImportLine(line, lineNumber) {
        const parts = line.split(',').map(part => part.trim());

        // 判断是红包还是幸运助手（通过字段数量和格式）
        if (parts.length === 6) {
            // 红包格式：直播间名,红包发送时间,开奖时间,当前轮次/总轮次,参与方式,快币数
            return this.parseRedpackLine(parts, lineNumber);
        } else if (parts.length === 7) {
            // 幸运助手格式：直播间名,参与条件,中奖人数,当前轮次/总轮次,开奖倒计时,定时发送时间,奖品说明
            return this.parseLuckyLine(parts, lineNumber);
        } else {
            return {
                success: false,
                error: `字段数量不正确，红包需要6个字段，幸运助手需要7个字段，当前有${parts.length}个字段`
            };
        }
    }

    // 解析红包行
    parseRedpackLine(parts, lineNumber) {
        const [roomName, sendTime, drawTime, rounds, participationType, coins] = parts;

        // 验证直播间
        const rooms = window.storageManager.getRooms();
        if (!rooms.includes(roomName)) {
            return { success: false, error: `直播间"${roomName}"不存在` };
        }

        // 验证时间格式
        if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(sendTime)) {
            return { success: false, error: `红包发送时间格式错误，应为"2025-08-30 18:00:00"` };
        }

        // 验证开奖时间
        const drawTimeValue = drawTime.replace('min', '');
        if (!['3', '10'].includes(drawTimeValue)) {
            return { success: false, error: `开奖时间只能是3min或10min` };
        }

        // 验证轮次
        const roundParts = rounds.split('/');
        if (roundParts.length !== 2) {
            return { success: false, error: `轮次格式错误，应为"1/4"` };
        }
        const currentRound = parseInt(roundParts[0]);
        const totalRounds = parseInt(roundParts[1]);
        if (isNaN(currentRound) || isNaN(totalRounds) || currentRound > totalRounds) {
            return { success: false, error: `轮次数值错误` };
        }

        // 验证参与方式
        const participationTypes = {
            '粉丝团红包': 'fanclub',
            '分享红包': 'share',
            '条件型普通快币红包': 'condition',
            '口令红包': 'password'
        };
        if (!participationTypes[participationType]) {
            return { success: false, error: `参与方式"${participationType}"不支持` };
        }

        // 验证快币数
        const coinsValue = parseInt(coins);
        if (isNaN(coinsValue) || coinsValue < 0 || coinsValue > 99999999) {
            return { success: false, error: `快币数必须是0-99999999之间的数字` };
        }

        return {
            success: true,
            product: {
                room: roomName,
                type: 'redpack',
                sendTime: sendTime.replace(' ', 'T'),
                drawTime: parseInt(drawTimeValue),
                currentRound: currentRound,
                totalRounds: totalRounds,
                participationType: participationTypes[participationType],
                coins: coinsValue,
                password: ''
            }
        };
    }

    // 解析幸运助手行
    parseLuckyLine(parts, lineNumber) {
        const [roomName, condition, winners, rounds, countdown, scheduledTime, prizeDescription] = parts;

        // 验证直播间
        const rooms = window.storageManager.getRooms();
        if (!rooms.includes(roomName)) {
            return { success: false, error: `直播间"${roomName}"不存在` };
        }

        // 验证参与条件
        const conditions = {
            '评论': 'comment',
            '点赞': 'like',
            '粉丝团': 'fanclub',
            '分享': 'share',
            '关注主播': 'follow',
            '观看时长': 'watchtime',
            '超粉团': 'superfan'
        };
        if (!conditions[condition]) {
            return { success: false, error: `参与条件"${condition}"不支持` };
        }

        // 验证中奖人数
        if (!winners.endsWith('人')) {
            return { success: false, error: `中奖人数必须以"人"结尾，如"100人"` };
        }
        const winnersValue = parseInt(winners.replace('人', ''));
        if (isNaN(winnersValue) || winnersValue < 1) {
            return { success: false, error: `中奖人数必须是大于0的数字` };
        }

        // 验证轮次
        const roundParts = rounds.split('/');
        if (roundParts.length !== 2) {
            return { success: false, error: `轮次格式错误，应为"1/4"` };
        }
        const currentRound = parseInt(roundParts[0]);
        const totalRounds = parseInt(roundParts[1]);
        if (isNaN(currentRound) || isNaN(totalRounds) || currentRound > totalRounds) {
            return { success: false, error: `轮次数值错误` };
        }

        // 验证开奖倒计时
        const countdownValue = countdown.replace('min', '');
        const countdownNum = parseInt(countdownValue);
        if (isNaN(countdownNum) || countdownNum < 1 || countdownNum > 60) {
            return { success: false, error: `开奖倒计时必须是1-60min之间` };
        }

        // 验证时间格式
        if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(scheduledTime)) {
            return { success: false, error: `定时发送时间格式错误，应为"2025-08-30 18:00:00"` };
        }

        return {
            success: true,
            product: {
                room: roomName,
                type: 'lucky',
                condition: conditions[condition],
                winners: winnersValue,
                currentRound: currentRound,
                totalRounds: totalRounds,
                countdown: countdownNum,
                scheduledTime: scheduledTime.replace(' ', 'T'),
                prizeDescription: prizeDescription,
                watchtime: 0
            }
        };
    }

    // 显示消息
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg text-white z-50 transition-all duration-300 transform translate-y-0 ${
            type === 'success' ? 'bg-green-600' :
            type === 'error' ? 'bg-red-600' :
            'bg-blue-600'
        }`;
        messageDiv.textContent = message;

        // 添加关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.className = 'ml-3 text-white hover:text-gray-200 font-bold text-lg';
        closeBtn.onclick = () => {
            messageDiv.style.transform = 'translateY(100%)';
            messageDiv.style.opacity = '0';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        };
        messageDiv.appendChild(closeBtn);

        document.body.appendChild(messageDiv);

        // 入场动画
        setTimeout(() => {
            messageDiv.style.transform = 'translateY(0)';
        }, 10);

        // 5秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.transform = 'translateY(100%)';
                messageDiv.style.opacity = '0';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }
        }, 5000);
    }
}

// 创建全局配置管理器实例
window.configManager = new ConfigManager();
