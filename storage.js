// 本地存储管理模块
class StorageManager {
    constructor() {
        this.STORAGE_KEY = 'livestream_config';
        this.defaultRooms = [
            'Before Party',
            '夏晚主会场（横屏）',
            '夏晚主会场（竖屏）',
            '夏晚第二现场'
        ];
    }

    // 获取所有配置数据
    getAllData() {
        const data = localStorage.getItem(this.STORAGE_KEY);
        if (data) {
            try {
                return JSON.parse(data);
            } catch (e) {
                console.error('解析存储数据失败:', e);
                return this.getDefaultData();
            }
        }
        return this.getDefaultData();
    }

    // 获取默认数据结构
    getDefaultData() {
        return {
            rooms: [...this.defaultRooms],
            products: [],
            lastUpdated: new Date().toISOString()
        };
    }

    // 保存所有配置数据
    saveAllData(data) {
        try {
            data.lastUpdated = new Date().toISOString();
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
            return true;
        } catch (e) {
            console.error('保存数据失败:', e);
            return false;
        }
    }

    // 获取直播间列表
    getRooms() {
        const data = this.getAllData();
        return data.rooms || [...this.defaultRooms];
    }

    // 添加直播间
    addRoom(roomName) {
        if (!roomName || roomName.trim() === '') return false;
        
        const data = this.getAllData();
        if (data.rooms.includes(roomName.trim())) {
            return false; // 直播间已存在
        }
        
        data.rooms.push(roomName.trim());
        return this.saveAllData(data);
    }

    // 删除直播间
    removeRoom(roomName) {
        const data = this.getAllData();
        const index = data.rooms.indexOf(roomName);
        if (index > -1) {
            data.rooms.splice(index, 1);
            // 同时删除该直播间下的所有产品配置
            data.products = data.products.filter(product => product.room !== roomName);
            return this.saveAllData(data);
        }
        return false;
    }

    // 获取产品配置列表
    getProducts() {
        const data = this.getAllData();
        return data.products || [];
    }

    // 添加产品配置
    addProduct(product) {
        const data = this.getAllData();
        product.id = this.generateId();
        product.createdAt = new Date().toISOString();
        data.products.push(product);
        return this.saveAllData(data) ? product.id : null;
    }

    // 更新产品配置
    updateProduct(productId, updates) {
        const data = this.getAllData();
        const index = data.products.findIndex(p => p.id === productId);
        if (index > -1) {
            data.products[index] = { ...data.products[index], ...updates };
            data.products[index].updatedAt = new Date().toISOString();
            return this.saveAllData(data);
        }
        return false;
    }

    // 删除产品配置
    removeProduct(productId) {
        const data = this.getAllData();
        const index = data.products.findIndex(p => p.id === productId);
        if (index > -1) {
            data.products.splice(index, 1);
            return this.saveAllData(data);
        }
        return false;
    }

    // 导出配置到文件
    exportConfig() {
        const data = this.getAllData();
        const configData = {
            ...data,
            exportTime: new Date().toISOString(),
            version: '1.0'
        };
        
        const blob = new Blob([JSON.stringify(configData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `livestream_config_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.ksconf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // 从文件导入配置
    importConfig(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const configData = JSON.parse(e.target.result);
                    
                    // 验证配置数据格式
                    if (!this.validateConfigData(configData)) {
                        reject(new Error('配置文件格式不正确'));
                        return;
                    }
                    
                    // 保存导入的配置
                    if (this.saveAllData(configData)) {
                        resolve(configData);
                    } else {
                        reject(new Error('保存配置失败'));
                    }
                } catch (error) {
                    reject(new Error('解析配置文件失败: ' + error.message));
                }
            };
            reader.onerror = () => reject(new Error('读取文件失败'));
            reader.readAsText(file);
        });
    }

    // 验证配置数据格式
    validateConfigData(data) {
        return data && 
               Array.isArray(data.rooms) && 
               Array.isArray(data.products) &&
               data.rooms.length > 0;
    }

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 清空所有数据
    clearAllData() {
        localStorage.removeItem(this.STORAGE_KEY);
    }

    // 清空所有产品配置
    clearAllProducts() {
        const data = this.getAllData();
        data.products = [];
        return this.saveAllData(data);
    }

    // 获取产品状态
    getProductStatus(product) {
        const now = new Date();
        const launchTime = new Date(product.sendTime || product.scheduledTime);

        if (now < launchTime) {
            return {
                status: 'pending',
                statusText: '待上线',
                phase: 'waiting'
            };
        }

        // 计算挂件下线时间
        let offlineTime;
        if (product.type === 'redpack') {
            // 红包：发送时间 + 开奖时间（分钟）
            offlineTime = new Date(launchTime.getTime() + (product.drawTime || 3) * 60 * 1000);
        } else if (product.type === 'lucky') {
            // 幸运助手：定时发送时间 + 开奖倒计时（分钟）
            offlineTime = new Date(launchTime.getTime() + (product.countdown || 5) * 60 * 1000);
        }

        if (now >= launchTime && now < offlineTime) {
            return {
                status: 'online',
                statusText: product.type === 'redpack' ? '红包挂件上线中' : '幸运助手挂件上线中',
                phase: 'active',
                offlineTime: offlineTime
            };
        }

        return {
            status: 'offline',
            statusText: '挂件已下线',
            phase: 'completed'
        };
    }

    // 获取按时间排序的产品列表
    getProductsByTime() {
        const products = this.getProducts();
        return products
            .filter(product => product.sendTime || product.scheduledTime)
            .sort((a, b) => {
                const timeA = new Date(a.sendTime || a.scheduledTime);
                const timeB = new Date(b.sendTime || b.scheduledTime);
                return timeA - timeB;
            });
    }

    // 获取需要在Dashboard显示的产品（待上线 + 挂件上线中）
    getDashboardProducts() {
        const now = new Date();
        const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

        return this.getProductsByTime().filter(product => {
            const status = this.getProductStatus(product);
            const launchTime = new Date(product.sendTime || product.scheduledTime);

            // 显示条件：
            // 1. 待上线且在未来24小时内
            // 2. 挂件上线中
            return (status.status === 'pending' && launchTime <= tomorrow) ||
                   status.status === 'online';
        });
    }

    // 获取下一个即将上线的产品（单个产品，用于兼容性）
    getNextProduct() {
        const products = this.getNextProducts();
        return products.length > 0 ? products[0] : null;
    }

    // 获取所有符合"下一个即将上线"条件的产品（用于轮播）
    getNextProducts() {
        const now = new Date();
        const products = this.getProductsByTime();

        // 只返回待上线的产品
        const pendingProducts = products.filter(product => {
            const status = this.getProductStatus(product);
            return status.status === 'pending';
        });

        if (pendingProducts.length === 0) {
            return [];
        }

        // 找到最早的上线时间
        const earliestTime = new Date(pendingProducts[0].sendTime || pendingProducts[0].scheduledTime);

        // 返回所有在同一时间（或很接近的时间，比如5分钟内）上线的产品
        const timeThreshold = 5 * 60 * 1000; // 5分钟的毫秒数
        const simultaneousProducts = pendingProducts.filter(product => {
            const productTime = new Date(product.sendTime || product.scheduledTime);
            return Math.abs(productTime - earliestTime) <= timeThreshold;
        });

        return simultaneousProducts;
    }
}

// 创建全局存储管理器实例
window.storageManager = new StorageManager();
