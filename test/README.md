# 测试文件夹

这个文件夹包含所有与测试、演示和开发相关的文件。

## 文件说明

### 测试页面
- `test.html` - 基础功能测试页面
- `test-modifications.html` - 第一轮修改功能测试页面
- `test-new-modifications.html` - 第二轮修改功能测试页面

### 演示页面
- `breathing-demo.html` - 呼吸灯效果演示
- `optimization-demo.html` - 性能优化演示
- `status-demo.html` - 状态显示演示

### 测试配置文件
- `test-config.ksconf` - 基础测试配置
- `optimized-test-config.ksconf` - 优化测试配置
- `status-test-config.ksconf` - 状态测试配置
- `urgent-test-config.ksconf` - 紧急情况测试配置

### 测试数据
- `batch-import-test.txt` - 批量导入功能测试数据

## 使用说明

1. **功能测试**: 打开相应的测试页面来验证特定功能
2. **演示展示**: 使用演示页面来展示特定效果
3. **配置测试**: 使用测试配置文件来快速加载测试数据
4. **批量导入测试**: 使用测试数据文件来验证批量导入功能

## 注意事项

- 所有测试和演示文件都应该放在这个文件夹中
- 新增的测试文件请遵循命名规范：`test-*` 或 `*-demo.html`
- 配置文件使用 `.ksconf` 扩展名
- 测试数据文件使用 `.txt` 扩展名

## 文件夹结构

```
test/
├── README.md                      # 本说明文件
├── test.html                      # 基础测试页面
├── test-modifications.html        # 修改功能测试页面
├── test-new-modifications.html    # 新修改功能测试页面
├── breathing-demo.html            # 呼吸灯演示
├── optimization-demo.html         # 优化演示
├── status-demo.html               # 状态演示
├── test-config.ksconf             # 基础测试配置
├── optimized-test-config.ksconf   # 优化测试配置
├── status-test-config.ksconf      # 状态测试配置
├── urgent-test-config.ksconf      # 紧急测试配置
└── batch-import-test.txt          # 批量导入测试数据
```
