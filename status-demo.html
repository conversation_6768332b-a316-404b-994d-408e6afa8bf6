<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品状态逻辑演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .breathing-text {
            animation: breathing 2s ease-in-out infinite;
        }
        .breathing-container {
            animation: breathing 2s ease-in-out infinite;
        }
        @keyframes breathing {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        @keyframes pulse-red {
            0%, 100% { 
                box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
            }
            50% { 
                box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
            }
        }
        .pulse-red {
            animation: pulse-red 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-4xl font-bold mb-8 text-center text-blue-400">🔄 产品状态逻辑演示</h1>
        
        <!-- 状态逻辑说明 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-6 text-purple-400">📋 新状态逻辑说明</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-700 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-3 text-orange-400">🎁 红包产品状态</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center space-x-2">
                            <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                            <span><strong>待上线：</strong>未到达红包发送时间</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                            <span><strong>挂件上线中：</strong>发送时间 → 发送时间+开奖时间</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-3 h-3 bg-gray-500 rounded-full"></span>
                            <span><strong>挂件已下线：</strong>开奖时间结束后</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-700 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-3 text-green-400">🍀 幸运助手产品状态</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center space-x-2">
                            <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                            <span><strong>待上线：</strong>未到达定时发送时间</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                            <span><strong>挂件上线中：</strong>定时发送时间 → 定时发送时间+开奖倒计时</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-3 h-3 bg-gray-500 rounded-full"></span>
                            <span><strong>挂件已下线：</strong>开奖倒计时结束后</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 状态演示 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- 待上线状态 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-blue-400">📅 待上线状态</h2>
                <div class="space-y-4">
                    <div class="bg-gray-700 border-l-4 border-blue-500 rounded p-3">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="text-orange-400 font-semibold">红包</span>
                                <span class="text-xs px-2 py-1 rounded bg-yellow-600 text-white">待上线</span>
                            </div>
                            <span class="text-sm text-yellow-400">15分钟 之后</span>
                        </div>
                        <div class="text-xs text-gray-400">第1/2轮 | 1500快币 | 粉丝团红包</div>
                    </div>
                    
                    <div class="bg-orange-900 border-l-4 border-orange-500 rounded p-3">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="text-green-400 font-semibold">幸运助手</span>
                                <span class="text-xs px-2 py-1 rounded bg-yellow-600 text-white">⚠️ 即将上线</span>
                            </div>
                            <span class="text-sm text-orange-300">3分钟 之后</span>
                        </div>
                        <div class="text-xs text-gray-400">第1/4轮 | 评论 | 5人中奖</div>
                    </div>
                </div>
            </div>
            
            <!-- 挂件上线中状态 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-green-400">🟢 挂件上线中</h2>
                <div class="space-y-4">
                    <div class="bg-green-900 border-l-4 border-green-500 rounded p-3">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="text-orange-400 font-semibold">红包</span>
                                <span class="text-xs px-2 py-1 rounded bg-green-600 text-white">红包挂件上线中</span>
                            </div>
                            <span class="text-sm text-green-300">7分钟后下线</span>
                        </div>
                        <div class="text-xs text-gray-400">第2/3轮 | 3000快币 | 口令红包 | 开奖倒计时：10分钟</div>
                        <div class="mt-2 p-2 bg-green-900 rounded text-green-200 text-xs">
                            <strong>🟢 运行中：</strong>挂件正在直播间中运行，请关注开奖情况
                        </div>
                    </div>
                    
                    <div class="bg-green-900 border-l-4 border-green-500 rounded p-3">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="text-green-400 font-semibold">幸运助手</span>
                                <span class="text-xs px-2 py-1 rounded bg-green-600 text-white">幸运助手挂件上线中</span>
                            </div>
                            <span class="text-sm text-green-300">5分钟后下线</span>
                        </div>
                        <div class="text-xs text-gray-400">第3/5轮 | 观看时长 | 10人中奖 | 开奖倒计时：8分钟</div>
                        <div class="mt-2 p-2 bg-green-900 rounded text-green-200 text-xs">
                            <strong>🟢 运行中：</strong>挂件正在直播间中运行，请关注开奖情况
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 挂件已下线状态 -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-400">⚫ 挂件已下线</h2>
                <div class="space-y-4">
                    <div class="bg-gray-600 border-l-4 border-gray-500 opacity-60 rounded p-3">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="text-orange-400 font-semibold">红包</span>
                                <span class="text-xs px-2 py-1 rounded bg-gray-600 text-white">✓ 挂件已下线</span>
                            </div>
                            <span class="text-sm text-gray-500">已完成</span>
                        </div>
                        <div class="text-xs text-gray-400">第1/1轮 | 800快币 | 分享红包</div>
                    </div>
                    
                    <div class="bg-gray-600 border-l-4 border-gray-500 opacity-60 rounded p-3">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="text-green-400 font-semibold">幸运助手</span>
                                <span class="text-xs px-2 py-1 rounded bg-gray-600 text-white">✓ 挂件已下线</span>
                            </div>
                            <span class="text-sm text-gray-500">已完成</span>
                        </div>
                        <div class="text-xs text-gray-400">第1/1轮 | 点赞 | 3人中奖</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Dashboard显示逻辑 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-6 text-cyan-400">📊 Dashboard显示逻辑</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-700 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-3 text-yellow-400">核心提醒 Dashboard</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-green-500">✓</span>
                            <span>显示<strong>待上线</strong>产品（未来24小时内）</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-green-500">✓</span>
                            <span>显示<strong>挂件上线中</strong>产品</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-red-500">✗</span>
                            <span>不显示<strong>挂件已下线</strong>产品</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-700 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-3 text-purple-400">详细时间线</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-green-500">✓</span>
                            <span>显示<strong>所有状态</strong>的产品</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-green-500">✓</span>
                            <span>按时间顺序排列</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-green-500">✓</span>
                            <span>不同状态使用不同颜色标识</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 时间计算示例 -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-2xl font-semibold mb-6 text-pink-400">⏰ 时间计算示例</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-700 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-3 text-orange-400">红包时间计算</h3>
                    <div class="space-y-3 text-sm">
                        <div class="bg-gray-800 rounded p-3">
                            <div class="text-blue-300 mb-1">发送时间：2025-08-24 19:00:00</div>
                            <div class="text-green-300 mb-1">开奖时间：3分钟</div>
                            <div class="text-yellow-300">下线时间：2025-08-24 19:03:00</div>
                        </div>
                        <div class="text-gray-300">
                            <strong>状态变化：</strong><br>
                            19:00:00前 → 待上线<br>
                            19:00:00-19:03:00 → 挂件上线中<br>
                            19:03:00后 → 挂件已下线
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-700 rounded-lg p-4">
                    <h3 class="text-lg font-semibold mb-3 text-green-400">幸运助手时间计算</h3>
                    <div class="space-y-3 text-sm">
                        <div class="bg-gray-800 rounded p-3">
                            <div class="text-blue-300 mb-1">定时发送：2025-08-24 20:00:00</div>
                            <div class="text-green-300 mb-1">开奖倒计时：5分钟</div>
                            <div class="text-yellow-300">下线时间：2025-08-24 20:05:00</div>
                        </div>
                        <div class="text-gray-300">
                            <strong>状态变化：</strong><br>
                            20:00:00前 → 待上线<br>
                            20:00:00-20:05:00 → 挂件上线中<br>
                            20:05:00后 → 挂件已下线
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-8">
            <a href="/" class="bg-blue-600 hover:bg-blue-700 px-8 py-4 rounded-lg transition-colors text-lg font-semibold mr-4">
                🚀 体验新状态逻辑
            </a>
            <button onclick="loadTestConfig()" class="bg-green-600 hover:bg-green-700 px-8 py-4 rounded-lg transition-colors text-lg font-semibold">
                📋 加载测试配置
            </button>
        </div>
    </div>

    <script>
        function loadTestConfig() {
            alert('请在主应用的配置管理中点击"加载配置"，然后选择 status-test-config.ksconf 文件来测试新的状态逻辑！');
        }
    </script>
</body>
</html>
